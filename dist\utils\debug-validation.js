/**
 * Debug Validation Utilities
 *
 * Comprehensive debugging tools for API key validation and provider testing.
 */
import { createOpenAIClient } from './openai-client.js';
import { getProvider } from './providers.js';
import { toError } from './error-utils.js';
/**
 * Comprehensive debug validation for a provider
 */
export async function debugValidateProvider(provider, apiKey) {
    const result = {
        provider,
        apiKeyPresent: false,
        providerConfigValid: false,
        clientCreated: false,
        modelsListSuccess: false,
        chatCompletionSuccess: false,
        details: [],
    };
    try {
        // Check API key
        if (apiKey && apiKey.trim().length > 0) {
            result.apiKeyPresent = true;
            result.details.push(`✓ API key present (length: ${apiKey.length})`);
        }
        else {
            result.details.push('✗ No API key provided');
            return result;
        }
        // Check provider configuration
        const providerConfig = getProvider(provider);
        if (providerConfig) {
            result.providerConfigValid = true;
            result.details.push(`✓ Provider config found: ${providerConfig.name}`);
            result.details.push(`  - Base URL: ${providerConfig.baseURL}`);
            result.details.push(`  - Default model: ${providerConfig.defaultModel}`);
            result.details.push(`  - Environment key: ${providerConfig.envKey}`);
        }
        else {
            result.details.push('✗ Provider configuration not found');
            return result;
        }
        // Try to create client
        try {
            const client = createOpenAIClient({ provider, apiKey });
            result.clientCreated = true;
            result.details.push('✓ OpenAI client created successfully');
            // Test models list endpoint
            try {
                const modelsResponse = await client.models.list();
                result.modelsListSuccess = true;
                result.details.push(`✓ Models list successful (${modelsResponse.data.length} models)`);
                // Log available models for debugging
                if (modelsResponse.data.length > 0) {
                    const modelNames = modelsResponse.data.map(m => m.id).slice(0, 5);
                    result.details.push(`  - Available models: ${modelNames.join(', ')}${modelsResponse.data.length > 5 ? '...' : ''}`);
                }
            }
            catch (modelsError) {
                const err = toError(modelsError);
                result.details.push(`✗ Models list failed: ${err.message}`);
            }
            // Test chat completion
            try {
                const model = providerConfig.defaultModel;
                result.details.push(`Testing chat completion with model: ${model}`);
                const chatResponse = await client.chat.completions.create({
                    model: model,
                    messages: [{ role: 'user', content: 'Hi' }],
                    max_tokens: 1,
                    temperature: 0.1,
                });
                if (chatResponse.choices && chatResponse.choices.length > 0) {
                    result.chatCompletionSuccess = true;
                    result.details.push('✓ Chat completion successful');
                    result.details.push(`  - Response: ${chatResponse.choices[0].message?.content || 'No content'}`);
                }
                else {
                    result.details.push('✗ Chat completion returned no choices');
                }
            }
            catch (chatError) {
                const err = toError(chatError);
                result.details.push(`✗ Chat completion failed: ${err.message}`);
                // Additional error details
                if ('response' in err) {
                    const response = err.response;
                    if (response?.status) {
                        result.details.push(`  - HTTP Status: ${response.status}`);
                    }
                    if (response?.data) {
                        result.details.push(`  - Response data: ${JSON.stringify(response.data)}`);
                    }
                }
            }
        }
        catch (clientError) {
            const err = toError(clientError);
            result.details.push(`✗ Failed to create client: ${err.message}`);
            result.error = err.message;
        }
    }
    catch (error) {
        const err = toError(error);
        result.error = err.message;
        result.details.push(`✗ Unexpected error: ${err.message}`);
    }
    return result;
}
/**
 * Print debug validation results
 */
export function printDebugResults(result) {
    console.log(`\n🔍 Debug Validation Results for ${result.provider}:`);
    console.log('='.repeat(50));
    result.details.forEach(detail => {
        console.log(detail);
    });
    console.log('\n📊 Summary:');
    console.log(`API Key Present: ${result.apiKeyPresent ? '✓' : '✗'}`);
    console.log(`Provider Config Valid: ${result.providerConfigValid ? '✓' : '✗'}`);
    console.log(`Client Created: ${result.clientCreated ? '✓' : '✗'}`);
    console.log(`Models List Success: ${result.modelsListSuccess ? '✓' : '✗'}`);
    console.log(`Chat Completion Success: ${result.chatCompletionSuccess ? '✓' : '✗'}`);
    if (result.error) {
        console.log(`\n❌ Error: ${result.error}`);
    }
    console.log('='.repeat(50));
}
/**
 * Quick debug test for DeepSeek specifically
 */
export async function debugDeepSeek(apiKey) {
    console.log('🚀 Starting DeepSeek Debug Validation...\n');
    const result = await debugValidateProvider('deepseek', apiKey);
    printDebugResults(result);
    // Additional DeepSeek-specific checks
    console.log('\n🔧 DeepSeek-Specific Checks:');
    console.log('- API Endpoint: https://api.deepseek.com/v1');
    console.log('- Expected Models: deepseek-chat, deepseek-reasoner');
    console.log('- Documentation: https://api-docs.deepseek.com/');
    if (!result.chatCompletionSuccess) {
        console.log('\n💡 Troubleshooting Tips:');
        console.log('1. Verify your API key at https://platform.deepseek.com/api_keys');
        console.log('2. Check your account status and credits');
        console.log('3. Ensure you\'re using the latest API key format');
        console.log('4. Try the API directly with curl to isolate the issue');
    }
}
//# sourceMappingURL=debug-validation.js.map